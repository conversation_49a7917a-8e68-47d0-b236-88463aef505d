import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>dal, Card, Button, Input } from 'antd';
import { mockDevices, Device } from '@/data/mockData';
import { formatCurrency } from '@/lib/utils';
import {
  Bot,
  X,
  Send,
  Cpu,
  HardDrive,
  Zap,
  MapPin,
  ShoppingCart,
  Sparkles,
  MessageCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface AIRecommendationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onRentDevice: (device: Device) => void;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface RecommendedDevice extends Device {
  matchScore: number;
  reason: string;
}

const AIRecommendationDialog: React.FC<AIRecommendationDialogProps> = ({
  isOpen,
  onClose,
  onRentDevice
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: '您好！我是SailFusion Cloud的AI助手。为了给您推荐最合适的算力资源，请告诉我您的具体需求。比如：\n\n• 您要运行什么类型的应用？\n• 对性能有什么要求？\n• 预算范围是多少？\n• 需要使用多长时间？',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [currentStep, setCurrentStep] = useState(0);
  const [userRequirements, setUserRequirements] = useState({
    application: '',
    performance: '',
    budget: '',
    duration: '',
    location: ''
  });
  const [recommendations, setRecommendations] = useState<RecommendedDevice[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到最新消息
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const conversationSteps = [
    {
      question: '请告诉我您要运行什么类型的应用？',
      key: 'application',
      suggestions: ['深度学习训练', '图像渲染', '数据分析', 'Web开发', '游戏服务器']
    },
    {
      question: '您对性能有什么具体要求吗？',
      key: 'performance',
      suggestions: ['高性能GPU', '大内存', '高CPU核心数', '快速存储', '无特殊要求']
    },
    {
      question: '您的预算范围是多少？（每小时）',
      key: 'budget',
      suggestions: ['5元以下', '5-10元', '10-20元', '20元以上', '预算充足']
    },
    {
      question: '预计需要使用多长时间？',
      key: 'duration',
      suggestions: ['几小时', '1-3天', '1周', '1个月', '长期使用']
    },
    {
      question: '对地理位置有偏好吗？',
      key: 'location',
      suggestions: ['上海', '北京', '广州', '就近选择', '无要求']
    }
  ];

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);

    // 保存用户回答
    if (currentStep < conversationSteps.length) {
      const currentStepData = conversationSteps[currentStep];
      setUserRequirements(prev => ({
        ...prev,
        [currentStepData.key]: inputMessage
      }));
    }

    setInputMessage('');
    setIsLoading(true);

    // 模拟AI思考时间
    setTimeout(() => {
      let aiResponse = '';

      if (currentStep < conversationSteps.length - 1) {
        // 继续下一个问题
        const nextStep = currentStep + 1;
        aiResponse = `好的，我了解了。${conversationSteps[nextStep].question}`;
        setCurrentStep(nextStep);
      } else {
        // 生成推荐
        aiResponse = '基于您的需求，我为您分析了所有可用的算力资源。正在生成个性化推荐...';
        generateRecommendations();
      }

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const generateRecommendations = () => {
    // 模拟AI推荐算法
    const availableDevices = mockDevices.filter(device =>
      !device.exclusive && device.status === 'online'
    );

    const scored = availableDevices.map(device => {
      let score = 0;
      let reasons = [];

      // 根据应用类型评分
      if (userRequirements.application.includes('深度学习') || userRequirements.application.includes('训练')) {
        if (device.type === 'GPU') score += 40;
        reasons.push('适合深度学习训练');
      }

      if (userRequirements.application.includes('渲染')) {
        if (device.type === 'GPU') score += 35;
        reasons.push('GPU渲染性能优秀');
      }

      // 根据性能要求评分
      if (userRequirements.performance.includes('GPU') && device.type === 'GPU') {
        score += 30;
        reasons.push('配备高性能GPU');
      }

      if (userRequirements.performance.includes('大内存')) {
        const memory = parseInt(device.memory);
        if (memory >= 64) score += 25;
        reasons.push('大内存配置');
      }

      // 根据预算评分
      if (userRequirements.budget.includes('5元以下') && device.pricePerHour <= 5) {
        score += 20;
        reasons.push('价格实惠');
      } else if (userRequirements.budget.includes('5-10元') && device.pricePerHour <= 10) {
        score += 15;
        reasons.push('性价比高');
      }

      // 根据地理位置评分
      if (userRequirements.location && device.location.includes(userRequirements.location)) {
        score += 10;
        reasons.push('地理位置优势');
      }

      return {
        ...device,
        matchScore: score,
        reason: reasons.join('，') || '基础配置满足需求'
      };
    });

    const topRecommendations = scored
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, 3);

    setRecommendations(topRecommendations);

    // 添加推荐结果消息
    setTimeout(() => {
      const recommendationMessage: ChatMessage = {
        id: (Date.now() + 2).toString(),
        type: 'ai',
        content: `根据您的需求分析，我为您推荐了${topRecommendations.length}个最匹配的算力资源。这些推荐综合考虑了您的应用场景、性能要求、预算和地理位置偏好。您可以查看详细信息并直接订购。`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, recommendationMessage]);
    }, 1500);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
            <Bot className="w-6 h-6 text-white" />
          </div>
          <div>
            <div className="text-xl font-bold text-gray-900">AI智能推荐</div>
            <p className="text-sm text-gray-600">让AI为您找到最合适的算力资源</p>
          </div>
        </div>
      }
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={1000}
      styles={{ body: { padding: 0, maxHeight: 'calc(90vh - 100px)', overflow: 'hidden' } }}
    >

      <div className="flex-1 flex overflow-hidden">
        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${message.type === 'user'
                    ? 'bg-primary text-white'
                    : 'bg-gray-100 text-gray-900'
                    }`}
                >
                  {message.type === 'ai' && (
                    <div className="flex items-center gap-2 mb-2">
                      <Sparkles className="w-4 h-4 text-purple-600" />
                      <span className="text-xs font-medium text-purple-600">AI助手</span>
                    </div>
                  )}
                  <div className="whitespace-pre-line">{message.content}</div>
                </div>
              </div>
            ))}

            {/* 滚动锚点 */}
            <div ref={messagesEndRef} />

            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-100 rounded-lg p-3">
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                    <span className="text-sm text-gray-600">AI正在思考...</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Input Area */}
          <div className="p-6 border-t">
            {currentStep < conversationSteps.length && (
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {conversationSteps[currentStep].suggestions.map((suggestion, index) => (
                    <Button
                      key={index}
                      type="default"
                      size="small"
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="text-xs"
                    >
                      {suggestion}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder="输入您的回答..."
                onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                disabled={isLoading}
              />
              <Button
                type="primary"
                onClick={handleSendMessage}
                disabled={isLoading || !inputMessage.trim()}
                icon={<Send className="w-4 h-4" />}
              />
            </div>
          </div>
        </div>

        {/* Recommendations Panel */}
        {recommendations.length > 0 && (
          <div className="w-96 border-l bg-gray-50 p-6 overflow-y-auto">
            <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-purple-600" />
              推荐结果
            </h3>

            <div className="space-y-4">
              {recommendations.map((device, index) => (
                <Card key={device.id} className="hover:shadow-md transition-shadow" size="small">
                  <div className="pb-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-sm font-medium">{device.name}</div>
                      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-2 py-1 rounded text-xs">
                        #{index + 1}
                      </div>
                    </div>
                    <div className="text-xs text-gray-600">{device.reason}</div>
                  </div>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex items-center gap-1">
                        <Cpu className="w-3 h-3" />
                        <span>{device.cpu}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <HardDrive className="w-3 h-3" />
                        <span>{device.memory}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Zap className="w-3 h-3" />
                        <span>{device.gpu}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        <span>{device.location}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="font-bold text-primary">
                        {formatCurrency(device.pricePerHour)}/小时
                      </span>
                      <Button
                        size="small"
                        type="primary"
                        onClick={() => {
                          onRentDevice(device);
                          onClose();
                        }}
                        icon={<ShoppingCart className="w-3 h-3" />}
                      >
                        订购
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default AIRecommendationDialog;
