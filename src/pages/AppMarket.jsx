import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRequest } from 'ahooks';
import {
  Card,
  Row,
  Col,
  Input,
  Button,
  Select,
  Tag,
  Space,
  Typography,
  Spin,
  Empty,
  Badge,
  Pagination,
  Tabs,
  Tooltip,
  Modal,
  Segmented,
  message,
} from 'antd';
import {
  Search,
  Download,
  Star,
  Eye,
  Code,
  Brain,
  Package,
  Sparkles,
  Play,
  Info,
} from 'lucide-react';
import {
  appTemplateService,
  APP_TYPES,
  APP_TYPE_LABELS,
  SORT_TYPES,
  SORT_TYPE_LABELS,
} from '@/services/appTemplateService';
import PageHeader from '@/components/PageHeader';
import { toast } from 'sonner';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const AppMarket = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [appTemplates, setAppTemplates] = useState([]);
  const [total, setTotal] = useState(0);
  const [selectedApp, setSelectedApp] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // 筛选和分页状态
  const [filters, setFilters] = useState({
    pageIndex: 1,
    pageSize: 12,
    keyword: '',
    appType: '',
    sortType: SORT_TYPES.RECOMMEND,
    tag: '',
    isOfficial: '',
  });

  // 应用类型图标映射
  const getAppTypeIcon = appType => {
    const iconMap = {
      [APP_TYPES.DEVELOPMENT_TOOL]: <Code className="w-5 h-5" />,
      [APP_TYPES.AI_MODEL]: <Brain className="w-5 h-5" />,
      [APP_TYPES.OPEN_SOURCE]: <Package className="w-5 h-5" />,
      [APP_TYPES.FANYI_PRODUCT]: <Sparkles className="w-5 h-5" />,
    };
    return iconMap[appType] || <Package className="w-5 h-5" />;
  };

  // 获取应用类型颜色
  const getAppTypeColor = appType => {
    const colorMap = {
      [APP_TYPES.DEVELOPMENT_TOOL]: 'blue',
      [APP_TYPES.AI_MODEL]: 'purple',
      [APP_TYPES.OPEN_SOURCE]: 'green',
      [APP_TYPES.FANYI_PRODUCT]: 'gold',
    };
    return colorMap[appType] || 'default';
  };

  // 加载应用模板列表
  const loadAppTemplates = async () => {
    setLoading(true);
    try {
      const data = await appTemplateService.getAppTemplates({
        ...filters,
        status: 1, // 只显示启用的应用
      });

      setAppTemplates(data.records || []);
      setTotal(data.total || 0);
    } catch (error) {
      console.error('加载应用列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = value => {
    setFilters(prev => ({
      ...prev,
      keyword: value,
      pageIndex: 1,
    }));
  };

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      pageIndex: 1,
    }));
  };

  // 处理分页变化
  const handlePageChange = (page, pageSize) => {
    setFilters(prev => ({
      ...prev,
      pageIndex: page,
      pageSize,
    }));
  };

  // 查看应用详情
  const handleViewDetail = app => {
    setSelectedApp(app); // 先设置基本信息
    setShowDetailModal(true); // 立即打开弹窗
  };

  // 部署应用
  const handleDeploy = async app => {
    try {
      // 增加下载次数
      await appTemplateService.incrementDownload(app.id);

      // 跳转到算力市场，并传递应用信息
      navigate('/compute-market', {
        state: { selectedApplication: app },
      });
    } catch (error) {
      console.error('部署应用失败:', error);
      // toast.error('部署应用失败');
    }
  };

  // 应用类型标签页
  const appTypeTabs = [
    { key: '', label: '全部', icon: <Package className="w-4 h-4" /> },
    {
      key: APP_TYPES.DEVELOPMENT_TOOL,
      label: '开发工具',
      icon: <Code className="w-4 h-4" />,
    },
    {
      key: APP_TYPES.AI_MODEL,
      label: '大模型',
      icon: <Brain className="w-4 h-4" />,
    },
    {
      key: APP_TYPES.OPEN_SOURCE,
      label: '开源应用',
      icon: <Package className="w-4 h-4" />,
    },
    {
      key: APP_TYPES.FANYI_PRODUCT,
      label: '帆一产品',
      icon: <Sparkles className="w-4 h-4" />,
    },
  ];

  useEffect(() => {
    loadAppTemplates();
  }, [filters]);

  return (
    <div className="min-h-screen">
      {/* 页面标题 */}
      <div className="mb-6">
        <Row justify="space-between" align="middle">
          <Col>
            <PageHeader
              title="应用市场"
              subtitle="发现和部署各种优质应用模板"
            />
          </Col>
          <Col>
            <Space>
              <Text type="secondary">共 {total} 个应用</Text>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 主要内容区域：左侧筛选，右侧应用列表 */}
      <Row gutter={[24, 24]}>
        {/* 左侧筛选面板 */}
        <Col xs={24} lg={6}>
          <Card
            title={
              <Space>
                <Search className="w-5 h-5" />
                筛选条件
              </Space>
            }
            extra={
              <Button
                type="text"
                size="small"
                onClick={() => {
                  setFilters({
                    pageIndex: 1,
                    pageSize: 12,
                    keyword: '',
                    appType: '',
                    sortType: SORT_TYPES.RECOMMEND,
                    tag: '',
                    isOfficial: '',
                  });
                }}
              >
                重置
              </Button>
            }
            className="sticky top-6"
          >
            <Space direction="vertical" size="large" className="w-full">
              {/* 搜索框 */}
              <div>
                <Title level={5} className="!mb-3">
                  搜索
                </Title>
                <Input.Search
                  placeholder="搜索应用名称、标签..."
                  value={filters.keyword}
                  onChange={e =>
                    setFilters(prev => ({ ...prev, keyword: e.target.value }))
                  }
                  onSearch={handleSearch}
                  allowClear
                />
              </div>

              {/* 应用类型 */}
              <div>
                <Title level={5} className="!mb-3 flex items-center gap-2">
                  <Package className="w-4 h-4 text-blue-500" />
                  应用类型
                </Title>
                <div className="space-y-2">
                  {appTypeTabs.map(tab => (
                    <Button
                      key={tab.key}
                      type={
                        filters.appType.toString() === tab.key.toString()
                          ? 'primary'
                          : 'default'
                      }
                      block
                      icon={tab.icon}
                      onClick={() =>
                        handleFilterChange(
                          'appType',
                          tab.key === '' ? '' : parseInt(tab.key)
                        )
                      }
                      className="justify-start"
                    >
                      {tab.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* 应用来源 */}
              <div>
                <Title level={5} className="!mb-3 flex items-center gap-2">
                  <Sparkles className="w-4 h-4 text-yellow-500" />
                  应用来源
                </Title>
                <Segmented
                  options={[
                    { label: '全部应用', value: '' },
                    { label: '官方应用', value: 1 },
                    { label: '社区应用', value: 0 },
                  ]}
                  value={filters.isOfficial}
                  onChange={value => handleFilterChange('isOfficial', value)}
                  block
                />
              </div>

              {/* 排序方式 */}
              <div>
                <Title level={5} className="!mb-3">
                  排序方式
                </Title>
                <Select
                  value={filters.sortType}
                  onChange={value => handleFilterChange('sortType', value)}
                  className="w-full"
                >
                  {Object.entries(SORT_TYPE_LABELS).map(([key, label]) => (
                    <Option key={key} value={key}>
                      {label}
                    </Option>
                  ))}
                </Select>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 右侧应用列表 */}
        <Col xs={24} lg={18}>
          <Spin spinning={loading}>
            {appTemplates.length === 0 ? (
              <Empty
                description="暂无应用"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <>
                <Row gutter={[16, 16]} className="mb-6">
                  {appTemplates.map(app => (
                    <Col xs={24} sm={12} lg={8} key={app.id}>
                      <AppCard
                        app={app}
                        onViewDetail={handleViewDetail}
                        onDeploy={handleDeploy}
                        getAppTypeIcon={getAppTypeIcon}
                        getAppTypeColor={getAppTypeColor}
                      />
                    </Col>
                  ))}
                </Row>

                {/* 分页 */}
                <div className="flex justify-center">
                  <Pagination
                    current={filters.pageIndex}
                    pageSize={filters.pageSize}
                    total={total}
                    onChange={handlePageChange}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }
                  />
                </div>
              </>
            )}
          </Spin>
        </Col>
      </Row>

      {/* 应用详情弹窗 */}
      <AppDetailModal
        app={selectedApp}
        visible={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        onDeploy={handleDeploy}
      />
    </div>
  );
};

// 应用卡片组件
const AppCard = ({
  app,
  onViewDetail,
  onDeploy,
  getAppTypeIcon,
  getAppTypeColor,
}) => {
  return (
    <Card
      hoverable
      className="h-full transition-all duration-300 hover:shadow-lg"
      styles={{
        body: { padding: '16px' },
      }}
    >
      {/* 应用头部 */}
      <div className="flex items-start gap-3 mb-3">
        {/* 应用图标 */}
        <div className="w-12 h-12 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center flex-shrink-0">
          {app.iconUrl ? (
            <img
              src={app.iconUrl}
              alt={app.name}
              className="w-8 h-8 object-cover rounded"
            />
          ) : (
            <div className="text-blue-600 text-lg">
              {getAppTypeIcon(app.appType)}
            </div>
          )}
        </div>

        {/* 应用信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <Title
              level={5}
              className="!mb-1 !text-base"
              ellipsis={{ tooltip: app.name }}
            >
              {app.name}
            </Title>
            {app.isOfficial === 1 && <Badge.Ribbon text="官方" color="blue" />}
          </div>

          <div className="flex items-center gap-2 mb-2">
            <Tag
              color={getAppTypeColor(app.appType)}
              size="small"
              className="py-1"
              icon={getAppTypeIcon(app.appType)}
            >
              {APP_TYPE_LABELS[app.appType]}
            </Tag>
            <Text type="secondary" className="text-xs">
              v{app.version}
            </Text>
          </div>
        </div>
      </div>

      {/* 应用描述 */}
      <Paragraph
        ellipsis={{ rows: 2, tooltip: app.description }}
        className="text-sm text-gray-600 mb-4 min-h-[40px]"
      >
        {app.description}
      </Paragraph>

      <div>
        {/* 应用标签 */}
        {app.tags && (
          <div className="mb-2">
            <div>
              {(typeof app.tags === 'string'
                ? app.tags.split(',')
                : app.tags || []
              ).map((tag, index) => (
                <Tag
                  bordered={false}
                  key={index}
                  color="cyan"
                  className="mr-1 mt-1"
                >
                  {typeof tag === 'string' ? tag.trim() : tag}
                </Tag>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 统计信息 */}
      <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
        <Space>
          <Download className="w-3 h-3" />
          <span>{app.downloadCount || 0} 下载</span>
        </Space>
        <Space>
          <Star className="w-3 h-3" />
          <span>{app.rating || 0} 评分</span>
        </Space>
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-2">
        <Button
          type="default"
          icon={<Eye className="w-3 h-3" />}
          onClick={() => onViewDetail(app)}
          className="flex-1"
        >
          详情
        </Button>
        <Button
          type="primary"
          icon={<Play className="w-3 h-3" />}
          onClick={() => onDeploy(app)}
          className="flex-1"
        >
          部署
        </Button>
      </div>
    </Card>
  );
};

// 应用详情弹窗组件
const AppDetailModal = ({ app, visible, onClose, onDeploy }) => {
  // 使用 useRequest 管理应用详情数据加载
  const { data: appDetail, loading } = useRequest(
    () => appTemplateService.getAppTemplateById(app?.id),
    {
      ready: visible && app?.id, // 只有在弹窗打开且有应用ID时才发起请求
      refreshDeps: [app?.id], // 当应用ID变化时重新请求
    }
  );

  if (!app) return null;

  // 使用详情数据或基本数据
  const displayApp = appDetail || app;

  return (
    <Modal
      title={
        <Space>
          <span>{app.name}</span>
          <Tag color="blue">v{app.version}</Tag>
          {app.isOfficial === 1 && <Tag color="gold">官方</Tag>}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button key="deploy" type="primary" onClick={() => onDeploy(app)}>
          立即部署
        </Button>,
      ]}
      styles={{
        body: { maxHeight: '60vh', overflow: 'auto' },
      }}
      getContainer={false} // 让 Modal 在 body 下渲染，避免样式冲突
    >
      <Spin spinning={loading}>
        <div className="space-y-4">
          <div>
            <Text strong>应用描述：</Text>
            <Paragraph>{displayApp.description}</Paragraph>
          </div>

          {displayApp.tags && (
            <div>
              <div className="mt-1">
                {(typeof displayApp.tags === 'string'
                  ? displayApp.tags.split(',')
                  : displayApp.tags || []
                ).map((tag, index) => (
                  <Tag bordered={false} color="cyan" key={index}>
                    {typeof tag === 'string' ? tag.trim() : tag}
                  </Tag>
                ))}
              </div>
            </div>
          )}

          <div>
            <Text strong>资源要求：</Text>
            <div className="mt-1 space-y-1">
              <div>CPU: 最少 {displayApp.minCpuCores || 0} 核</div>
              <div>内存: 最少 {displayApp.minMemoryGb || 0} GB</div>
              {displayApp.minGpuCount > 0 && (
                <div>GPU: 最少 {displayApp.minGpuCount} 卡</div>
              )}
              <div>系统盘: {displayApp.systemDiskSize || 0} GB</div>
            </div>
          </div>

          {displayApp.protocolPorts &&
            Array.isArray(displayApp.protocolPorts) &&
            displayApp.protocolPorts.length > 0 && (
              <div>
                <Text strong>端口配置：</Text>
                <div className="mt-1">
                  {displayApp.protocolPorts.map((port, index) => (
                    <Tag key={index}>
                      {port.protocol}: {port.port}
                    </Tag>
                  ))}
                </div>
              </div>
            )}

          {displayApp.readme && (
            <div>
              <Text strong>使用说明：</Text>
              <div className="mt-1 p-3 bg-gray-50 rounded-md">
                <pre className="whitespace-pre-wrap text-sm">
                  {displayApp.readme}
                </pre>
              </div>
            </div>
          )}
        </div>
      </Spin>
    </Modal>
  );
};
export default AppMarket;
